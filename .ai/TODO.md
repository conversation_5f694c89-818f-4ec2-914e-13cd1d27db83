# TODO List for NGINX Domain Review Tool

This is a structured TODO list.

Items use markdown formatting as examples.

- [ ] Add a new feature
- [-] Partially complete
- [x] Completed

Completed items MUST be MARKED and moved to the DONE section. Immediately after completion before a commit.
The commit MUST have a corresponding change in TODO.md with the code change.

Don't create random tests output files! For test use `python3 nginx_domain_review.py @c.args --html --max-domains 10 > c.out.html` and check `c.out.html`.

For testing generated html use URL http://127.0.0.1:8080/c.out.html

---

## TODO

### Datatable report.html



## LATER

- [ ] whois asi jen generovat link na nejaky whois web u domeny kde to ma smysl podle pravidel TLD https://www.whois.com/whois/DOMENA

- [ ] novy typ input soubory weblog, log z nginx apache apod, najit prvni domenu na radku (pokud je za ni :cislo_portu, tak ho odstranit), aby dokazal zpracovat ruzne formaty

- [ ] our_redirect nestaci ze domena je v nasem seznamu, jeste musime kontrolovat jestli domena je "our", ale to budeme vedet a to cele projedeme, to by vyzadovalo asi postprocesing nad ziskanymi daty
- [ ] ukladani nastaveni DataTable - ma na to nativne funkci, skuzit
- [ ] presety pro nastaveni filtru na html, jako button neco jako vyber sloupcu
    - nevede na nas
    - vede na nas, ale redirect pryc
    - vede na nas, ale nema header
    - vede na nas, ale error
    - vede na nas a nema https nebo nema https_redirect nebo chyba certifikatu

## MAYBE

- udelat to podle logiky v python kodech presne stejnou logiku v typescript jako PWA aplikace, misto CLI by byl formular se stejnymi moznostmi

## DONE

- [x] Sort columns by click don't change direction, only trigger on direction of sorting repeatedly.
- [x] Compact show/hide columns button and search input too.
- [x] Compact whole header with button - minimal balas, important is table data.
- [x] html po zmene viditelnosti sloupcu, znovu aplikovat search pokud je nastaven, kod vidim ale asi nefunguje
- [x] html vpravo nahore pocitadlo vyfiltrovanych a vsech radku
- [x] html seach podpora pro hledani vyrazu !NEGATE, !, !!, jako ve filtru sloupcu
- [x] filter sloupcu nefunguje!!! - spravil jsem, nevim proc to navazal na viditelnist sloupcu, smazal jsem to
- [x] HTML Search aby hledal jen ve viditelnych (kdyz vypnu cname, tak aby v nem nehledal)
- [x] HTML pro https ERROR a LOOP nedelat http.
- [x] vice header sloupcu, --add-headers comaseparated list, dynamisky prida sloupce s hotnotou header odpovedi do zakladni http dotaz se start_protokol
- [x] WHOIS sloupec
- [x] otestovat redirect to Location: /page.html nebrat jako redirect, protoze to uz je jen presmerovnani v ramci domeny

